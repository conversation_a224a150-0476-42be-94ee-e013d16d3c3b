'use client';

import Script from 'next/script';
import { usePathname } from 'next/navigation';

export default function ConditionalScriptLoader() {
  const pathname = usePathname();
  
  // Don't load any custom scripts on admin or auth pages
  const isAdminOrAuth = pathname?.startsWith('/admin') || pathname?.startsWith('/auth');
  
  if (isAdminOrAuth) {
    // Return nothing for admin/auth pages - no custom scripts at all
    return null;
  }

  return (
    <>
      {/* jQuery Library File */}
      <Script src="/js/jquery-3.7.1.min.js?v=2" strategy="afterInteractive" />
      
      {/* Bootstrap js file */}
      <Script src="/js/bootstrap.min.js?v=2" strategy="afterInteractive" />
      
      {/* Validator js file */}
      <Script src="/js/validator.min.js?v=2" strategy="afterInteractive" />
      
      {/* SlickNav js file */}
      <Script src="/js/jquery.slicknav.js?v=2" strategy="afterInteractive" />
      
      {/* Swiper js file */}
      <Script src="/js/swiper-bundle.min.js?v=2" strategy="afterInteractive" />
      
      {/* Counter js file */}
      <Script src="/js/jquery.waypoints.min.js?v=2" strategy="afterInteractive" />
      <Script src="/js/jquery.counterup.min.js?v=2" strategy="afterInteractive" />
      
      {/* Magnific js file */}
      <Script src="/js/jquery.magnific-popup.min.js?v=2" strategy="afterInteractive" />
      
      {/* SmoothScroll */}
      <Script src="/js/SmoothScroll.js?v=2" strategy="afterInteractive" />
      
      {/* Parallax js */}
      <Script src="/js/parallaxie.js?v=2" strategy="afterInteractive" />
      
      {/* MagicCursor js file - EXCLUDED from admin/auth pages */}
      <Script src="/js/gsap.min.js?v=2" strategy="afterInteractive" />
      <Script src="/js/magiccursor.js?v=2" strategy="afterInteractive" />
      
      {/* Text Effect js file */}
      <Script src="/js/SplitText.js?v=2" strategy="afterInteractive" />
      <Script src="/js/ScrollTrigger.min.js?v=2" strategy="afterInteractive" />
      
      {/* YTPlayer js File */}
      <Script src="/js/jquery.mb.YTPlayer.min.js?v=2" strategy="afterInteractive" />
      
      {/* Wow js file */}
      <Script src="/js/wow.min.js?v=2" strategy="afterInteractive" />
      
      {/* Main Custom js file */}
      <Script src="/js/function.js?v=2" strategy="afterInteractive" />

      {/* Image Fix Script - Load last to ensure it runs after all other scripts */}
      <Script src="/js/image-fix.js?v=1" strategy="afterInteractive" />
    </>
  );
}
