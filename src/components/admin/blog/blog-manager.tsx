'use client';

import React, { useState, useEffect, useRef } from 'react';
import {
  EyeSlashIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PencilIcon,
  TrashIcon,
  PowerIcon,
  DocumentTextIcon,
  CalendarIcon,
  TagIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { BlogModal } from './blog-modal';
import { CrudConfig } from '../crud/types';
import { AdminHeader } from '../shared/admin-header';

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  featuredImageUrl?: string;
  authorId?: string;
  isPublished: boolean;
  publishedAt?: string;
  categories?: string;
  tags?: string;
  createdAt: string;
  updatedAt: string;
  [key: string]: any;
}

interface BlogManagerProps {
  config: CrudConfig<BlogPost>;
}

// BlogAvatar Component
interface BlogAvatarProps {
  title: string;
  featuredImageUrl?: string | null;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full-height';
  className?: string;
  style?: React.CSSProperties;
}

function BlogAvatar({ 
  title, 
  featuredImageUrl, 
  size = 'md', 
  className = '',
  style = {}
}: BlogAvatarProps) {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  // Size configurations
  const sizeClasses = {
    xs: 'w-6 h-6',
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24',
    'full-height': 'w-full h-full'
  };

  const iconSizes = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12',
    'full-height': 'w-16 h-16'
  };

  const textSizes = {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
    'full-height': 'text-4xl'
  };

  // Generate initials from blog title
  const getInitials = (title: string) => {
    return title
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Generate a consistent color based on the blog title
  const getBackgroundColor = (title: string) => {
    const colors = [
      'bg-blue-500',
      'bg-green-500',
      'bg-purple-500',
      'bg-pink-500',
      'bg-indigo-500',
      'bg-yellow-500',
      'bg-red-500',
      'bg-teal-500',
      'bg-orange-500',
      'bg-cyan-500'
    ];
    
    let hash = 0;
    for (let i = 0; i < title.length; i++) {
      hash = title.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  };

  const handleImageLoad = () => {
    setImageLoading(false);
    setImageError(false);
  };

  const handleImageError = () => {
    setImageLoading(false);
    setImageError(true);
  };

  const baseClasses = `
    ${sizeClasses[size]} 
    rounded-lg 
    flex 
    items-center 
    justify-center 
    overflow-hidden 
    ${size === 'full-height' ? 'min-h-[320px]' : ''}
    ${className}
  `;

  // If we have a valid featured image URL and no error, show the image
  if (featuredImageUrl && !imageError) {
    return (
      <div 
        className={`${baseClasses} bg-gray-100 relative`}
        style={style}
      >
        {imageLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 w-6 h-6"></div>
          </div>
        )}
        <img
          src={featuredImageUrl}
          alt={`${title} featured image`}
          className={`
            ${size === 'full-height' ? 'w-full h-full object-cover' : 'w-full h-full object-cover'}
            ${imageLoading ? 'opacity-0' : 'opacity-100'}
            transition-opacity duration-200
          `}
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
      </div>
    );
  }

  // Fallback: Show initials with colored background
  return (
    <div 
      className={`
        ${baseClasses} 
        ${getBackgroundColor(title)} 
        text-white 
        font-semibold 
        ${textSizes[size]}
        shadow-sm
      `}
      style={style}
      title={title}
    >
      {size === 'full-height' ? (
        <div className="flex flex-col items-center justify-center space-y-4">
          <DocumentTextIcon className="w-24 h-24 text-white opacity-80" />
          <div className="text-center px-4">
            <div className="text-2xl font-bold mb-2">{getInitials(title)}</div>
            <div className="text-sm opacity-90 break-words line-clamp-3">{title}</div>
          </div>
        </div>
      ) : (
        <span>{getInitials(title)}</span>
      )}
    </div>
  );
}

export function BlogManagerNew({ config }: BlogManagerProps) {
  // State management
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [sortBy, setSortBy] = useState(config.defaultSort?.field || 'updatedAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>(
    config.defaultSort?.direction === 'asc' ? 'asc' : 'desc'
  );
  const [selectedPosts, setSelectedPosts] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'list' | 'grid' | 'card'>(
    config.defaultViewSettings?.mode === 'list' ||
    config.defaultViewSettings?.mode === 'grid' ||
    config.defaultViewSettings?.mode === 'card'
      ? config.defaultViewSettings.mode
      : 'list'
  );
  const [density, setDensity] = useState<'compact' | 'comfortable' | 'spacious'>(
    config.defaultViewSettings?.density === 'compact' ||
    config.defaultViewSettings?.density === 'comfortable' ||
    config.defaultViewSettings?.density === 'spacious'
      ? (config.defaultViewSettings.density as 'compact' | 'comfortable' | 'spacious')
      : 'comfortable'
  );

  // Create a wrapper function for setDensity
  const handleDensityChange = (newDensity: 'compact' | 'comfortable' | 'spacious') => {
    setDensity(newDensity);
  };
  const [visibleColumns, setVisibleColumns] = useState<string[]>(
    Array.isArray(config.defaultViewSettings?.visibleColumns)
      ? config.defaultViewSettings.visibleColumns
      : []
  );
  const [filters, setFilters] = useState<Record<string, string>>({});
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingPost, setEditingPost] = useState<BlogPost | null>(null);

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
      setCurrentPage(1); // Reset to first page when searching
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Fetch blog posts
  const fetchBlogPosts = async (preserveFocus = false) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: (config.pageSize || 10).toString(),
        search: debouncedSearchQuery,
        sortBy,
        sortOrder,
      });

      // Add filters to params
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          params.append(key, value);
        }
      });

      console.log('Fetching blog posts with params:', params.toString()); // Debug log

      const response = await fetch(`/api/admin/${config.endpoint}?${params}`);
      if (!response.ok) throw new Error('Failed to fetch blog posts');

      const data = await response.json();
      console.log('Received blog posts data:', data); // Debug log

      setBlogPosts(data.data || []);
      setTotalPages(data.pagination?.totalPages || 1);
      setError(null); // Clear any previous errors on successful fetch
    } catch (err) {
      console.error('Error fetching blog posts:', err); // Debug log
      setError(err instanceof Error ? err.message : 'Failed to fetch blog posts');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Preserve focus when searching
    const isSearching = debouncedSearchQuery !== '';
    fetchBlogPosts(isSearching);
  }, [currentPage, debouncedSearchQuery, sortBy, sortOrder, filters]);

  // Handle create
  const handleCreate = async (formData: any) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (!response.ok) throw new Error('Failed to create blog post');

      setIsCreateModalOpen(false);
      fetchBlogPosts();
      alert('Blog post created successfully!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create blog post';
      setError(errorMessage);
      alert(errorMessage);
      throw err;
    }
  };

  // Handle update
  const handleUpdate = async (id: string, formData: any) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (!response.ok) throw new Error('Failed to update blog post');

      setIsEditModalOpen(false);
      setEditingPost(null);
      fetchBlogPosts();
      alert('Blog post updated successfully!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update blog post';
      setError(errorMessage);
      alert(errorMessage);
      throw err;
    }
  };

  // Handle delete
  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to delete blog post');

      fetchBlogPosts();
      alert('Blog post deleted successfully!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete blog post';
      setError(errorMessage);
      alert(errorMessage);
      throw err;
    }
  };

  // Handle bulk actions
  const handleBulkAction = async (action: string) => {
    if (selectedPosts.length === 0) return;

    setActionLoading(action);
    try {
      const promises = selectedPosts.map(async (id) => {
        switch (action) {
          case 'publish':
            return fetch(`/api/admin/${config.endpoint}/${id}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ isPublished: true }),
            });
          case 'unpublish':
            return fetch(`/api/admin/${config.endpoint}/${id}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ isPublished: false }),
            });
          case 'delete':
            return fetch(`/api/admin/${config.endpoint}/${id}`, {
              method: 'DELETE',
            });
          default:
            throw new Error(`Unknown bulk action: ${action}`);
        }
      });

      await Promise.all(promises);
      setSelectedPosts([]);
      fetchBlogPosts();
      alert(`Bulk ${action} completed successfully!`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : `Failed to ${action} blog posts`;
      setError(errorMessage);
      alert(errorMessage);
    } finally {
      setActionLoading(null);
    }
  };

  // Handle individual actions
  const handleAction = async (action: string, item: BlogPost) => {
    setActionLoading(`${action}-${item.id}`);
    try {
      switch (action) {
        case 'edit':
          setEditingPost(item);
          setIsEditModalOpen(true);
          break;

        case 'delete':
          const confirmMessage = 'Are you sure you want to delete this blog post? This action cannot be undone.';
          if (window.confirm(confirmMessage)) {
            await handleDelete(item.id);
          }
          break;

        case 'toggle-published':
          await handleUpdate(item.id, { isPublished: !item.isPublished });
          break;

        default:
          console.warn(`Unknown action: ${action}`);
      }
    } finally {
      setActionLoading(null);
    }
  };

  // Handle sorting
  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
    setCurrentPage(1);
  };

  // Handle selection
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedPosts(blogPosts.map(post => post.id));
    } else {
      setSelectedPosts([]);
    }
  };

  const handleSelectPost = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedPosts([...selectedPosts, id]);
    } else {
      setSelectedPosts(selectedPosts.filter(postId => postId !== id));
    }
  };

  // Get visible fields for table
  const getVisibleFields = () => {
    if (visibleColumns.length > 0) {
      return config.fields?.filter(field => visibleColumns.includes(field.key)) || [];
    }
    return config.fields || [];
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Truncate text
  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <div className="space-y-6">
      <AdminHeader
        title={config.title}
        description={config.description}
        searchPlaceholder={config.searchPlaceholder}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        enableSearch={config.enableSearch}
        enableFilters={config.enableFilters}
        enableViewControls={config.enableViewControls}
        enableDensityControls={config.enableDensityControls}
        enableColumnVisibility={config.enableColumnVisibility}
        enableCreate={config.permissions?.create}
        onCreateClick={() => setIsCreateModalOpen(true)}
        createButtonText="Add Blog Post"
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        density={density}
                        onDensityChange={handleDensityChange}
        visibleColumns={visibleColumns}
        onVisibleColumnsChange={setVisibleColumns}
        availableColumns={config.fields?.map(field => ({ key: field.key, label: field.label })) || []}
        filters={config.filters}
        onFiltersChange={setFilters}
        currentFilters={filters}
        itemCount={blogPosts.length}
        totalItems={blogPosts.length}
      />

      {/* Bulk Actions */}
      {config.enableBulkActions && selectedPosts.length > 0 && (
        <div className="mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16 mb-4">
          <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-300 rounded-lg shadow-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm font-bold text-blue-800">
                {selectedPosts.length} blog post(s) selected
              </span>
              <div className="flex items-center gap-3">
                <button
                  onClick={() => handleBulkAction('publish')}
                  className="px-4 py-2 text-sm font-bold bg-white text-green-700 rounded-lg hover:bg-green-50 transition-colors shadow-md border-2 border-green-500"
                >
                  Publish
                </button>
                <button
                  onClick={() => handleBulkAction('unpublish')}
                  className="px-4 py-2 text-sm font-bold bg-white text-orange-700 rounded-lg hover:bg-orange-50 transition-colors shadow-md border-2 border-orange-500"
                >
                  Unpublish
                </button>
                <button
                  onClick={() => handleBulkAction('delete')}
                  className="px-4 py-2 text-sm font-bold bg-white text-red-700 rounded-lg hover:bg-red-50 transition-colors shadow-md border-2 border-red-500"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <XMarkIcon className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
                <div className="mt-4">
                  <button
                    onClick={() => {
                      setError(null);
                      fetchBlogPosts();
                    }}
                    className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
                  >
                    Try Again
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16">
          <div className="bg-white rounded-lg border border-gray-200 p-8">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Loading blog posts...</span>
            </div>
          </div>
        </div>
      )}

      {/* Data Display */}
      {!loading && !error && (
        <div className="mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16">
          <div className="bg-white rounded-lg border border-gray-200">
            {blogPosts.length === 0 ? (
              /* Empty State */
              <div className="p-12 text-center">
                <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No blog posts found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {debouncedSearchQuery ? 'Try adjusting your search terms.' : 'Get started by creating your first blog post.'}
                </p>
                {config.permissions?.create && !debouncedSearchQuery && (
                  <div className="mt-6">
                    <button
                      onClick={() => setIsCreateModalOpen(true)}
                      className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Add Blog Post
                    </button>
                  </div>
                )}
              </div>
                         ) : viewMode === 'list' ? (
               /* Table View */
               <div className="overflow-hidden">
                 
                  <style jsx>{`
                    .density-compact th, .density-compact td {
                      padding-top: 4px !important;
                      padding-bottom: 4px !important;
                    }
                    .density-comfortable th, .density-comfortable td {
                      padding-top: 16px !important;
                      padding-bottom: 16px !important;
                    }
                    .density-spacious th, .density-spacious td {
                      padding-top: 32px !important;
                      padding-bottom: 32px !important;
                    }
                  `}</style>
                                    <div className="overflow-x-auto">
                     <table className={`min-w-full divide-y divide-gray-200 density-${density}`}>
                    <thead className="bg-gray-50">
                      <tr>
                        {/* Checkbox Column */}
                        {config.enableBulkActions && (
                                                     <th className="w-4 px-6">
                            <input
                              type="checkbox"
                              checked={selectedPosts.length === blogPosts.length && blogPosts.length > 0}
                              onChange={(e) => handleSelectAll(e.target.checked)}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white"
                              style={{ backgroundColor: 'white' }}
                            />
                          </th>
                        )}

                        {/* Data Columns */}
                        {getVisibleFields().map((field) => (
                          <th
                            key={field.key}
                            className="px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            <button
                              onClick={() => handleSort(field.key)}
                              className="flex items-center space-x-1 hover:text-gray-700"
                            >
                              <span>{field.label}</span>
                              {sortBy === field.key && (
                                sortOrder === 'asc' ? <ArrowUpIcon className="w-3 h-3" /> : <ArrowDownIcon className="w-3 h-3" />
                              )}
                            </button>
                          </th>
                        ))}

                        {/* Actions Column */}
                        {config.actions && config.actions.length > 0 && (
                                                   <th className="px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        )}
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {blogPosts.map((post) => (
                        <tr key={post.id} className={`hover:bg-gray-50 ${
                          selectedPosts.includes(post.id) ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                        }`}>
                          {/* Checkbox */}
                          {config.enableBulkActions && (
                                                         <td className="px-6">
                              <input
                                type="checkbox"
                                checked={selectedPosts.includes(post.id)}
                                onChange={(e) => handleSelectPost(post.id, e.target.checked)}
                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white"
                                style={{ backgroundColor: 'white' }}
                              />
                            </td>
                          )}

                          {/* Data Cells */}
                          {getVisibleFields().map((field) => (
                            <td key={field.key}                             className="px-6 whitespace-nowrap">
                              {field.key === 'title' ? (
                                <div className="flex items-center">
                                  <BlogAvatar
                                    title={post.title}
                                    featuredImageUrl={post.featuredImageUrl}
                                    size="sm"
                                    className="mr-3"
                                  />
                                  <div>
                                    <div className="text-sm font-medium text-gray-900">{post.title}</div>
                                    <div className="text-sm text-gray-500">{post.slug}</div>
                                  </div>
                                </div>
                              ) : field.key === 'isPublished' ? (
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  post.isPublished
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-gray-100 text-gray-800'
                                }`}>
                                  {post.isPublished ? 'Published' : 'Draft'}
                                </span>
                              ) : field.key === 'excerpt' || field.key === 'content' ? (
                                <div className="text-sm text-gray-900 max-w-xs truncate" title={post[field.key]}>
                                  {truncateText(post[field.key] || '', 50)}
                                </div>
                              ) : field.key === 'tags' || field.key === 'categories' ? (
                                <div className="text-sm text-gray-900">
                                  {post[field.key] ? (
                                    <div className="flex flex-wrap gap-1">
                                      {(post[field.key] as string).split(',').slice(0, 2).map((tag: string, index: number) => (
                                        <span key={index} className="inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                                          {tag.trim()}
                                        </span>
                                      ))}
                                      {(post[field.key] as string).split(',').length > 2 && (
                                        <span className="text-xs text-gray-500">+{(post[field.key] as string).split(',').length - 2} more</span>
                                      )}
                                    </div>
                                  ) : '-'}
                                </div>
                              ) : field.key === 'createdAt' || field.key === 'updatedAt' || field.key === 'publishedAt' ? (
                                <div className="text-sm text-gray-500">
                                  {post[field.key] ? formatDate(post[field.key] as string) : '-'}
                                </div>
                              ) : (
                                <div className="text-sm text-gray-900">
                                  {post[field.key] || '-'}
                                </div>
                              )}
                            </td>
                          ))}

                          {/* Actions */}
                          {config.actions && config.actions.length > 0 && (
                                                       <td className="px-6 whitespace-nowrap text-right text-sm font-medium">
                              <div className="flex items-center space-x-2">
                                {config.actions.map((action) => {
                                  const isLoading = actionLoading === `${action.action}-${post.id}`;
                                  const IconComponent = action.icon === 'EyeIcon' ? EyeSlashIcon :
                                                     action.icon === 'PencilIcon' ? PencilIcon :
                                                     action.icon === 'PowerIcon' ? PowerIcon :
                                                     action.icon === 'TrashIcon' ? TrashIcon : DocumentTextIcon;

                                  return (
                                    <button
                                      key={action.action}
                                      onClick={() => handleAction(action.action, post)}
                                      disabled={isLoading}
                                      className={`p-1 rounded-md transition-colors ${
                                        action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' :
                                        action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' :
                                        action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' :
                                        action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' :
                                        'text-gray-600 hover:bg-gray-50'
                                      } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                                      title={action.tooltip}
                                    >
                                      {isLoading ? (
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                                      ) : (
                                        <IconComponent className="w-4 h-4" />
                                      )}
                                    </button>
                                  );
                                })}
                              </div>
                            </td>
                          )}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            ) : viewMode === 'grid' ? (
              /* Grid View */
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {blogPosts.map((post) => (
                    <div key={post.id} className={`bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow ${
                      selectedPosts.includes(post.id) ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                    }`}>
                      {/* Featured Image */}
                      <div className="aspect-video bg-gray-100 rounded-t-lg overflow-hidden">
                        {post.featuredImageUrl ? (
                          <img
                            src={post.featuredImageUrl}
                            alt={post.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <DocumentTextIcon className="w-12 h-12 text-gray-400" />
                          </div>
                        )}
                      </div>

                      {/* Content */}
                      <div className="p-4">
                        {/* Title */}
                        <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                          {post.title}
                        </h3>

                        {/* Excerpt */}
                        {post.excerpt && (
                          <p className="text-sm text-gray-600 mb-3 line-clamp-3">
                            {post.excerpt}
                          </p>
                        )}

                        {/* Meta Information */}
                        <div className="space-y-2 mb-4">
                          {/* Status */}
                          <div className="flex items-center justify-between">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              post.isPublished
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {post.isPublished ? 'Published' : 'Draft'}
                            </span>
                            <span className="text-xs text-gray-500">
                              {formatDate(post.updatedAt)}
                            </span>
                          </div>

                          {/* Categories */}
                          {post.categories && (
                            <div className="flex flex-wrap gap-1">
                              {post.categories.split(',').slice(0, 2).map((category: string, index: number) => (
                                <span key={index} className="inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                                  {category.trim()}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>

                        {/* Actions */}
                        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                          <div className="flex items-center space-x-2">
                            {config.actions?.map((action) => {
                              const isLoading = actionLoading === `${action.action}-${post.id}`;
                              const IconComponent = action.icon === 'EyeIcon' ? EyeSlashIcon :
                                                 action.icon === 'PencilIcon' ? PencilIcon :
                                                 action.icon === 'PowerIcon' ? PowerIcon :
                                                 action.icon === 'TrashIcon' ? TrashIcon : DocumentTextIcon;

                              return (
                                <button
                                  key={action.action}
                                  onClick={() => handleAction(action.action, post)}
                                  disabled={isLoading}
                                  className={`p-1.5 rounded-md transition-colors ${
                                    action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' :
                                    action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' :
                                    action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' :
                                    action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' :
                                    'text-gray-600 hover:bg-gray-50'
                                  } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                                  title={action.tooltip}
                                >
                                  {isLoading ? (
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                                  ) : (
                                    <IconComponent className="w-4 h-4" />
                                  )}
                                </button>
                              );
                            })}
                          </div>

                          {/* Checkbox for bulk actions */}
                          {config.enableBulkActions && (
                            <input
                              type="checkbox"
                              checked={selectedPosts.includes(post.id)}
                              onChange={(e) => handleSelectPost(post.id, e.target.checked)}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white"
                              style={{ backgroundColor: 'white' }}
                            />
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              /* Card View */
              <div className="p-6">
                <div className="space-y-4">
                  {blogPosts.map((post) => (
                    <div key={post.id} className={`bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow ${
                      selectedPosts.includes(post.id) ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                    }`}>
                      <div className="flex">
                        {/* Featured Image */}
                        <div className="w-48 h-32 bg-gray-100 rounded-l-lg overflow-hidden flex-shrink-0">
                          {post.featuredImageUrl ? (
                            <img
                              src={post.featuredImageUrl}
                              alt={post.title}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <DocumentTextIcon className="w-8 h-8 text-gray-400" />
                            </div>
                          )}
                        </div>

                        {/* Content */}
                        <div className="flex-1 p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              {/* Title and Slug */}
                              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                                {post.title}
                              </h3>
                              <p className="text-sm text-gray-500 mb-2">
                                {post.slug}
                              </p>

                              {/* Excerpt */}
                              {post.excerpt && (
                                <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                                  {post.excerpt}
                                </p>
                              )}

                              {/* Meta Information */}
                              <div className="flex items-center space-x-4 text-sm text-gray-500">
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  post.isPublished
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-gray-100 text-gray-800'
                                }`}>
                                  {post.isPublished ? 'Published' : 'Draft'}
                                </span>
                                <span>Updated: {formatDate(post.updatedAt)}</span>
                                {post.categories && (
                                  <span>Category: {post.categories.split(',')[0].trim()}</span>
                                )}
                              </div>
                            </div>

                            {/* Actions */}
                            <div className="flex items-center space-x-2 ml-4">
                              {config.actions?.map((action) => {
                                const isLoading = actionLoading === `${action.action}-${post.id}`;
                                const IconComponent = action.icon === 'EyeIcon' ? EyeSlashIcon :
                                                   action.icon === 'PencilIcon' ? PencilIcon :
                                                   action.icon === 'PowerIcon' ? PowerIcon :
                                                   action.icon === 'TrashIcon' ? TrashIcon : DocumentTextIcon;

                                return (
                                  <button
                                    key={action.action}
                                    onClick={() => handleAction(action.action, post)}
                                    disabled={isLoading}
                                    className={`p-2 rounded-md transition-colors ${
                                      action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' :
                                      action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' :
                                      action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' :
                                      action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' :
                                      'text-gray-600 hover:bg-gray-50'
                                    } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                                    title={action.tooltip}
                                  >
                                    {isLoading ? (
                                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                                    ) : (
                                      <IconComponent className="w-4 h-4" />
                                    )}
                                  </button>
                                );
                              })}

                              {/* Checkbox for bulk actions */}
                              {config.enableBulkActions && (
                                <input
                                  type="checkbox"
                                  checked={selectedPosts.includes(post.id)}
                                  onChange={(e) => handleSelectPost(post.id, e.target.checked)}
                                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white"
                                  style={{ backgroundColor: 'white' }}
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Modals */}
      <BlogModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreate}
        title="Create New Blog Post"
      />

      <BlogModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setEditingPost(null);
        }}
        onSubmit={async (formData) => {
          if (editingPost) {
            await handleUpdate(editingPost.id, formData);
          }
        }}
        title="Edit Blog Post"
        initialData={editingPost ?? undefined}
      />
    </div>
  );
}
