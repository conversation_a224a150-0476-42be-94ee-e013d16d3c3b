'use client'

import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  DocumentTextIcon, 
  CalendarIcon, 
  TagIcon,
  PhotoIcon,
  EyeIcon,
  EyeSlashIcon,
  XMarkIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

// Modal Components (copied from Modal.tsx)
interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  subtitle?: string
  children: React.ReactNode
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl'
  showCloseButton?: boolean
  closeOnBackdrop?: boolean
  className?: string
}

const maxWidthClasses = {
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  '2xl': 'max-w-2xl',
  '3xl': 'max-w-3xl',
  '4xl': 'max-w-4xl',
  '5xl': 'max-w-5xl',
  '6xl': 'max-w-6xl',
  '7xl': 'max-w-7xl',
}

export function Modal({
  isOpen,
  onClose,
  title,
  subtitle,
  children,
  maxWidth = '6xl',
  showCloseButton = true,
  closeOnBackdrop = true,
  className = ''
}: ModalProps) {
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (closeOnBackdrop && e.target === e.currentTarget) {
      onClose()
    }
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 bg-black/60 backdrop-blur-sm"
          onClick={handleBackdropClick}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ type: "spring", duration: 0.5 }}
            className="flex min-h-screen items-center justify-center p-2"
            onClick={(e) => e.stopPropagation()}
          >
            <div className={`w-full ${maxWidthClasses[maxWidth]} max-h-[95vh] !bg-white rounded-xl shadow-2xl flex flex-col overflow-hidden ${className}`}>
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-100 !bg-gradient-to-r !from-gray-50 !to-white">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <span className="text-lg font-bold text-blue-600">T</span>
                  </div>
                  <div>
                    <h2 className="text-lg font-bold text-gray-900">{title}</h2>
                    {subtitle && (
                      <p className="text-xs text-gray-500">{subtitle}</p>
                    )}
                  </div>
                </div>
                {showCloseButton && (
                  <button
                    onClick={onClose}
                    className="w-7 h-7 rounded-lg bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
                  >
                    <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>

              {/* Content */}
              <div className="flex-1 overflow-y-auto">
                {children}
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// Modal Header Component
interface ModalHeaderProps {
  title: string
  subtitle?: string
  icon?: React.ReactNode
  onClose?: () => void
  showCloseButton?: boolean
}

export function ModalHeader({ title, subtitle, icon, onClose, showCloseButton = true }: ModalHeaderProps) {
  return (
    <div className="flex items-center justify-between p-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white">
      <div className="flex items-center space-x-2">
        {icon && (
          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            {icon}
          </div>
        )}
        <div>
          <h2 className="text-lg font-bold text-gray-900">{title}</h2>
          {subtitle && (
            <p className="text-xs text-gray-500">{subtitle}</p>
          )}
        </div>
      </div>
      {showCloseButton && onClose && (
        <button
          onClick={onClose}
          className="w-7 h-7 rounded-lg bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
        >
          <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </div>
  )
}

// Modal Content Component
interface ModalContentProps {
  children: React.ReactNode
  className?: string
}

export function ModalContent({ children, className = '' }: ModalContentProps) {
  return (
    <div className={`flex-1 overflow-y-auto !bg-white ${className}`}>
      {children}
    </div>
  )
}

// Modal Footer Component
interface ModalFooterProps {
  children: React.ReactNode
  className?: string
}

export function ModalFooter({ children, className = '' }: ModalFooterProps) {
  return (
    <div className={`border-t border-gray-100 !bg-gray-50 p-3 ${className}`}>
      {children}
    </div>
  )
}

// Form Section Component
interface FormSectionProps {
  title: string
  icon?: React.ReactNode
  iconColor?: 'blue' | 'green' | 'purple' | 'orange' | 'red'
  children: React.ReactNode
  className?: string
}

const iconColorClasses = {
  blue: 'bg-blue-100 text-blue-600',
  green: 'bg-green-100 text-green-600',
  purple: 'bg-purple-100 text-purple-600',
  orange: 'bg-orange-100 text-orange-600',
  red: 'bg-red-100 text-red-600',
}

export function FormSection({ title, icon, iconColor = 'blue', children, className = '' }: FormSectionProps) {
  return (
    <div className={`!bg-white rounded-lg border border-gray-100 p-4 shadow-sm ${className}`}>
      <div className="flex items-center space-x-2 mb-3">
        {icon && (
          <div className={`w-5 h-5 ${iconColorClasses[iconColor]} rounded flex items-center justify-center`}>
            {icon}
          </div>
        )}
        <h3 className="text-base font-semibold text-gray-900">{title}</h3>
      </div>
      {children}
    </div>
  )
}

// Form Field Component
interface FormFieldProps {
  label: string
  required?: boolean
  error?: string
  children: React.ReactNode
  className?: string
}

export function FormField({ label, required = false, error, children, className = '' }: FormFieldProps) {
  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      {children}
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
    </div>
  )
}

// Input Component
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string
}

export function Input({ error, className = '', ...props }: InputProps) {
  return (
    <input
      {...props}
      className={`w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors !bg-white !text-black ${error ? 'border-red-300' : ''} ${className}`}
    />
  )
}

// Textarea Component
interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: string
}

export function Textarea({ error, className = '', ...props }: TextareaProps) {
  return (
    <textarea
      {...props}
      className={`w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none !bg-white !text-black ${error ? 'border-red-300' : ''} ${className}`}
    />
  )
}

// Button Component
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'danger' | 'success' | 'warning' | 'info'
  size?: 'sm' | 'md' | 'lg'
  loading?: boolean
}

const buttonVariants = {
  primary: 'bg-gray-200 text-black hover:bg-gray-300 border-gray-300 shadow-md hover:shadow-lg',
  secondary: 'bg-gray-200 text-black hover:bg-gray-300 border-gray-300 shadow-md hover:shadow-lg',
  outline: 'text-black bg-gray-100 border-2 border-gray-400 hover:bg-gray-200 hover:border-gray-500 shadow-md hover:shadow-lg',
  danger: 'bg-gray-200 text-black hover:bg-gray-300 border-gray-300 shadow-md hover:shadow-lg',
  success: 'bg-gray-200 text-black hover:bg-gray-300 border-gray-300 shadow-md hover:shadow-lg',
  warning: 'bg-gray-200 text-black hover:bg-gray-300 border-gray-300 shadow-md hover:shadow-lg',
  info: 'bg-gray-200 text-black hover:bg-gray-300 border-gray-300 shadow-md hover:shadow-lg',
}

const buttonSizes = {
  sm: 'px-2 py-1 text-sm',
  md: 'px-3 py-2',
  lg: 'px-4 py-3 text-lg',
}

export function Button({ 
  variant = 'primary', 
  size = 'md', 
  loading = false, 
  disabled, 
  children, 
  className = '', 
  ...props 
}: ButtonProps) {
  return (
    <button
      {...props}
      disabled={disabled || loading}
      className={`rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 border ${buttonVariants[variant]} ${buttonSizes[size]} ${className}`}
    >
      {loading ? (
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
          <span>Loading...</span>
        </div>
      ) : (
        children
      )}
    </button>
  )
}

// File Upload Component with Inline Layout
interface FileUploadProps {
  label: string
  accept?: string
  onFileSelect: (file: File) => void
  selectedFile?: File | null
  previewUrl?: string | null
  onClear?: () => void
  error?: string
  className?: string
  filePath?: string
  onFilePathChange?: (path: string) => void
}

export function FileUpload({ 
  label, 
  accept = "image/*", 
  onFileSelect, 
  selectedFile, 
  previewUrl, 
  onClear, 
  error, 
  className = '',
  filePath = '',
  onFilePathChange
}: FileUploadProps) {
  const fileInputRef = React.useRef<HTMLInputElement>(null)

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      onFileSelect(file)
    }
  }

  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label}
      </label>
      
      <div className="space-y-3">
        {/* Inline File Upload Layout */}
        <div className="flex items-center space-x-3">
          {/* File Path Input */}
          {onFilePathChange && (
            <div className="flex-1">
              <Input
                type="text"
                placeholder="Enter file path or URL..."
                value={filePath}
                onChange={(e) => onFilePathChange(e.target.value)}
              />
            </div>
          )}
          
          {/* Choose File Button */}
          <div className="flex-shrink-0">
            <input
              ref={fileInputRef}
              type="file"
              accept={accept}
              onChange={handleFileSelect}
              className="hidden"
            />
            <Button
              type="button"
              variant="info"
              onClick={() => fileInputRef.current?.click()}
              className="!bg-gray-500 hover:!bg-gray-600 !border-gray-600"
            >
              Choose File
            </Button>
          </div>

          {/* Image Preview */}
          {previewUrl && (
            <div className="flex-shrink-0">
              <div className="relative">
                <img
                  src={previewUrl}
                  alt="Preview"
                  className="w-16 h-12 object-cover border border-gray-200 rounded-md bg-gray-50"
                />
                {onClear && (
                  <button
                    type="button"
                    onClick={onClear}
                    className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
                  >
                    ×
                  </button>
                )}
              </div>
            </div>
          )}
        </div>

        {/* File Info */}
        {selectedFile && (
          <div className="flex items-center space-x-2 p-2 bg-gray-50 rounded-md">
            <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span className="text-sm text-gray-700 truncate">{selectedFile.name}</span>
            <span className="text-xs text-gray-500">({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)</span>
            {onClear && (
              <Button
                type="button"
                variant="danger"
                size="sm"
                onClick={onClear}
              >
                Clear
              </Button>
            )}
          </div>
        )}

        {error && (
          <p className="text-sm text-red-600">{error}</p>
        )}
      </div>
    </div>
  )
}

// Image Preview Component
interface ImagePreviewProps {
  src: string
  alt?: string
  onRemove?: () => void
  className?: string
}

export function ImagePreview({ src, alt = "Preview", onRemove, className = '' }: ImagePreviewProps) {
  return (
    <div className={`relative inline-block ${className}`}>
      <img
        src={src}
        alt={alt}
        className="w-32 h-24 object-cover border border-gray-200 rounded-md bg-gray-50"
      />
      {onRemove && (
        <button
          type="button"
          onClick={onRemove}
          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
        >
          ×
        </button>
      )}
    </div>
  )
}

interface BlogPost {
  id?: string
  title: string
  slug: string
  content: string
  excerpt?: string
  featuredImageUrl?: string
  authorId?: string
  isPublished: boolean
  publishedAt?: string
  categories?: string
  tags?: string
  createdAt?: string
  updatedAt?: string
}

interface BlogModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: BlogPost) => Promise<void>
  title: string
  initialData?: BlogPost
}

export function BlogModal({
  isOpen,
  onClose,
  onSubmit,
  title,
  initialData
}: BlogModalProps) {
  // Add global error handler for unhandled promise rejections
  React.useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason);
      event.preventDefault(); // Prevent the default error handling
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);
  const [formData, setFormData] = useState<BlogPost>({
    title: initialData?.title || '',
    slug: initialData?.slug || '',
    content: initialData?.content || '',
    excerpt: initialData?.excerpt || '',
    featuredImageUrl: initialData?.featuredImageUrl || '',
    authorId: initialData?.authorId || '',
    isPublished: initialData?.isPublished ?? false,
    publishedAt: initialData?.publishedAt || '',
    categories: initialData?.categories || '',
    tags: initialData?.tags || '',
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(initialData?.featuredImageUrl || null)
  const [activeTab, setActiveTab] = useState<'content' | 'settings' | 'preview'>('content')
  const [showPreview, setShowPreview] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Reset form when modal opens/closes or initialData changes
  useEffect(() => {
    if (isOpen) {
      setFormData({
        title: initialData?.title || '',
        slug: initialData?.slug || '',
        content: initialData?.content || '',
        excerpt: initialData?.excerpt || '',
        featuredImageUrl: initialData?.featuredImageUrl || '',
        authorId: initialData?.authorId || '',
        isPublished: initialData?.isPublished ?? false,
        publishedAt: initialData?.publishedAt || '',
        categories: initialData?.categories || '',
        tags: initialData?.tags || '',
      })
      setPreviewUrl(initialData?.featuredImageUrl || null)
      setSelectedFile(null)
      setActiveTab('content')
    }
  }, [isOpen, initialData])

  // Auto-generate slug from title
  useEffect(() => {
    if (formData.title && !initialData) {
      const slug = formData.title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()
      setFormData(prev => ({ ...prev, slug }))
    }
  }, [formData.title, initialData])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  const handleFileSelect = (file: File) => {
    try {
      setSelectedFile(file)
      
      // Create preview URL
      const reader = new FileReader()
      reader.onload = (e) => {
        setPreviewUrl(e.target?.result as string)
      }
      reader.onerror = (e) => {
        console.error('File reader error:', e)
        setPreviewUrl(null)
      }
      reader.readAsDataURL(file)
    } catch (error) {
      console.error('File selection error:', error)
      setSelectedFile(null)
      setPreviewUrl(null)
    }
  }

  const handleFileUpload = async (): Promise<string | null> => {
    if (!selectedFile) return null

    const formData = new FormData()
    formData.append('file', selectedFile)
    formData.append('type', 'blog-featured-image')

    try {
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.error || `Upload failed with status: ${response.status}`
        console.error('Upload API error:', errorMessage)
        return null // Return null instead of throwing
      }

      const data = await response.json()
      if (!data.url) {
        console.error('No URL returned from upload')
        return null // Return null instead of throwing
      }
      
      return data.url
    } catch (error) {
      console.error('File upload error:', error)
      return null // Return null instead of throwing
    }
  }

  const clearFile = () => {
    setSelectedFile(null)
    setPreviewUrl(null)
    setFormData(prev => ({ ...prev, featuredImageUrl: '' }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      let featuredImageUrl = formData.featuredImageUrl

      // Upload file if selected
      if (selectedFile) {
        const uploadedUrl = await handleFileUpload()
        if (uploadedUrl) {
          featuredImageUrl = uploadedUrl
        } else {
          // If upload failed, keep the existing URL or empty string
          console.warn('File upload failed, continuing with existing image URL')
        }
      }

      const submitData: BlogPost = {
        title: formData.title,
        slug: formData.slug,
        content: formData.content,
        excerpt: formData.excerpt,
        featuredImageUrl: featuredImageUrl,
        authorId: formData.authorId || undefined,
        isPublished: formData.isPublished,
        publishedAt: formData.publishedAt || undefined,
        categories: formData.categories,
        tags: formData.tags,
      }

      await onSubmit(submitData)
      onClose()
    } catch (error) {
      console.error('Submit error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to save blog post'
      alert(`Error: ${errorMessage}. Please try again.`)
    } finally {
      setIsSubmitting(false)
    }
  }

  const getWordCount = (text: string) => {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length
  }

  const getReadingTime = (text: string) => {
    const wordsPerMinute = 200
    const wordCount = getWordCount(text)
    return Math.ceil(wordCount / wordsPerMinute)
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      subtitle="Create and manage your blog content with our advanced editor"
      maxWidth="7xl"
    >
      <ModalContent>
        <div className="flex h-[80vh]">
          {/* Left Sidebar - Navigation */}
          <div className="w-64 bg-gradient-to-b from-slate-50 to-slate-100 border-r border-slate-200 p-4">
            <div className="space-y-4">
              {/* Tab Navigation */}
              <div className="space-y-2">
                <button
                  onClick={() => setActiveTab('content')}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                    activeTab === 'content'
                      ? 'bg-gray-200 text-black border border-gray-300 shadow-sm'
                      : 'text-slate-600 hover:bg-gray-200 hover:text-black'
                  }`}
                >
                  <DocumentTextIcon className="w-5 h-5" />
                  <span className="font-medium">Content</span>
                </button>
                
                <button
                  onClick={() => setActiveTab('settings')}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                    activeTab === 'settings'
                      ? 'bg-gray-200 text-black border border-gray-300 shadow-sm'
                      : 'text-slate-600 hover:bg-gray-200 hover:text-black'
                  }`}
                >
                  <TagIcon className="w-5 h-5" />
                  <span className="font-medium">Settings</span>
                </button>
                
                <button
                  onClick={() => setActiveTab('preview')}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                    activeTab === 'preview'
                      ? 'bg-gray-200 text-black border border-gray-300 shadow-sm'
                      : 'text-slate-600 hover:bg-gray-200 hover:text-black'
                  }`}
                >
                  <EyeIcon className="w-5 h-5" />
                  <span className="font-medium">Preview</span>
                </button>
              </div>

              {/* Content Stats */}
              <div className="!bg-white rounded-lg p-4 border border-slate-200">
                <h3 className="text-sm font-semibold text-slate-700 mb-3">Content Stats</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-slate-600">Words:</span>
                    <span className="font-medium">{getWordCount(formData.content)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Reading time:</span>
                    <span className="font-medium">{getReadingTime(formData.content)} min</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Characters:</span>
                    <span className="font-medium">{formData.content.length}</span>
                  </div>
                </div>
              </div>

              {/* Publishing Status */}
              <div className="!bg-white rounded-lg p-4 border border-slate-200">
                <h3 className="text-sm font-semibold text-slate-700 mb-3">Publishing Status</h3>
                <div className="flex items-center space-x-2">
                  {formData.isPublished ? (
                    <>
                      <CheckCircleIcon className="w-5 h-5 text-green-500" />
                      <span className="text-sm text-green-700 font-medium">Published</span>
                    </>
                  ) : (
                    <>
                      <ExclamationTriangleIcon className="w-5 h-5 text-yellow-500" />
                      <span className="text-sm text-yellow-700 font-medium">Draft</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="flex-1 overflow-y-auto">
            {activeTab === 'content' && (
              <div className="p-6 space-y-6">
                {/* Featured Image Section */}
                <div className="!bg-white rounded-xl border border-slate-200 p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <PhotoIcon className="w-6 h-6 text-blue-600" />
                    <h3 className="text-lg font-semibold text-slate-900">Featured Image</h3>
                  </div>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <FileUpload
                        label="Upload Featured Image"
                        accept="image/*"
                        onFileSelect={handleFileSelect}
                        selectedFile={selectedFile}
                        previewUrl={previewUrl}
                        onClear={clearFile}
                        filePath={formData.featuredImageUrl}
                        onFilePathChange={(path) => setFormData(prev => ({ ...prev, featuredImageUrl: path }))}
                      />
                    </div>
                    
                    {previewUrl && (
                      <div className="relative">
                        <img
                          src={previewUrl}
                          alt="Featured image preview"
                          className="w-full h-48 object-cover rounded-lg border border-slate-200"
                        />
                        <button
                          onClick={clearFile}
                          className="absolute top-2 right-2 w-8 h-8 bg-gray-200 text-black rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors"
                        >
                          <XMarkIcon className="w-4 h-4" />
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Title and Slug */}
                <div className="!bg-white rounded-xl border border-slate-200 p-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <FormField label="Blog Title" required>
                      <Input
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        required
                        placeholder="Enter your blog post title..."
                        className="text-lg font-medium"
                      />
                    </FormField>

                    <FormField label="URL Slug" required>
                      <Input
                        type="text"
                        name="slug"
                        value={formData.slug}
                        onChange={handleInputChange}
                        required
                        placeholder="blog-post-slug"
                        className="font-mono"
                      />
                    </FormField>
                  </div>
                </div>

                {/* Excerpt */}
                <div className="!bg-white rounded-xl border border-slate-200 p-6">
                  <FormField label="Excerpt">
                    <Textarea
                      name="excerpt"
                      value={formData.excerpt}
                      onChange={handleInputChange}
                      placeholder="Write a brief description of your blog post..."
                      rows={3}
                      className="resize-none"
                    />
                    <p className="text-xs text-slate-500 mt-2">
                      This will appear in blog previews and search results
                    </p>
                  </FormField>
                </div>

                {/* Main Content */}
                <div className="!bg-white rounded-xl border border-slate-200 p-6">
                  <FormField label="Blog Content" required>
                    <Textarea
                      name="content"
                      value={formData.content}
                      onChange={handleInputChange}
                      required
                      placeholder="Write your blog post content here..."
                      rows={12}
                      className="resize-none text-base leading-relaxed"
                    />
                  </FormField>
                </div>
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="p-6 space-y-6">
                {/* Author and Publishing */}
                <div className="bg-white rounded-xl border border-slate-200 p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <CalendarIcon className="w-6 h-6 text-blue-600" />
                    <h3 className="text-lg font-semibold text-slate-900">Publishing Settings</h3>
                  </div>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <FormField label="Author ID">
                      <Input
                        type="text"
                        name="authorId"
                        value={formData.authorId}
                        onChange={handleInputChange}
                        placeholder="author-123"
                      />
                    </FormField>

                    <FormField label="Publish Date">
                      <Input
                        type="datetime-local"
                        name="publishedAt"
                        value={formData.publishedAt}
                        onChange={handleInputChange}
                      />
                    </FormField>
                  </div>

                  <div className="mt-6">
                    <label className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="checkbox"
                        name="isPublished"
                        checked={formData.isPublished}
                        onChange={handleInputChange}
                        className="w-4 h-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                      />
                      <span className="text-sm font-medium text-slate-700">
                        Publish this blog post immediately
                      </span>
                    </label>
                    <p className="text-xs text-slate-500 mt-2">
                      Unpublished posts will be saved as drafts
                    </p>
                  </div>
                </div>

                {/* Categories and Tags */}
                <div className="!bg-white rounded-xl border border-slate-200 p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <TagIcon className="w-6 h-6 text-blue-600" />
                    <h3 className="text-lg font-semibold text-slate-900">Categories & Tags</h3>
                  </div>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <FormField label="Categories">
                      <Input
                        type="text"
                        name="categories"
                        value={formData.categories}
                        onChange={handleInputChange}
                        placeholder="Web Development, Technology"
                      />
                      <p className="text-xs text-slate-500 mt-2">Separate with commas</p>
                    </FormField>

                    <FormField label="Tags">
                      <Input
                        type="text"
                        name="tags"
                        value={formData.tags}
                        onChange={handleInputChange}
                        placeholder="react, nextjs, javascript"
                      />
                      <p className="text-xs text-slate-500 mt-2">Separate with commas</p>
                    </FormField>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'preview' && (
              <div className="p-6">
                <div className="!bg-white rounded-xl border border-slate-200 p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-semibold text-slate-900">Blog Post Preview</h3>
                    <Button
                      onClick={() => setShowPreview(!showPreview)}
                      variant="info"
                      className="flex items-center space-x-2"
                    >
                      {showPreview ? (
                        <>
                          <EyeSlashIcon className="w-4 h-4" />
                          <span>Hide Preview</span>
                        </>
                      ) : (
                        <>
                          <EyeIcon className="w-4 h-4" />
                          <span>Show Preview</span>
                        </>
                      )}
                    </Button>
                  </div>

                  {showPreview && (
                    <div className="prose max-w-none">
                      {previewUrl && (
                        <img
                          src={previewUrl}
                          alt="Featured image"
                          className="w-full h-64 object-cover rounded-lg mb-6"
                        />
                      )}
                      
                      <h1 className="text-3xl font-bold text-slate-900 mb-4">
                        {formData.title || 'Blog Post Title'}
                      </h1>
                      
                      {formData.excerpt && (
                        <p className="text-lg text-slate-600 mb-6 italic">
                          {formData.excerpt}
                        </p>
                      )}
                      
                      <div className="text-sm text-slate-500 mb-6">
                        <span>By {formData.authorId || 'Author'}</span>
                        {formData.publishedAt && (
                          <>
                            <span className="mx-2">•</span>
                            <span>{new Date(formData.publishedAt).toLocaleDateString()}</span>
                          </>
                        )}
                      </div>
                      
                      <div className="whitespace-pre-wrap text-slate-700 leading-relaxed">
                        {formData.content || 'Your blog content will appear here...'}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </ModalContent>

      <ModalFooter>
        <div className="flex items-center justify-between w-full">
          <div className="text-sm text-slate-600">
            {isSubmitting ? (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                <span>Saving your blog post...</span>
              </div>
            ) : (
              <span>Ready to save your blog post</span>
            )}
          </div>
          
          <div className="flex items-center space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            
            <Button
              type="submit"
              onClick={handleSubmit}
              loading={isSubmitting}
              disabled={isSubmitting}
              variant="success"
            >
              {formData.isPublished ? 'Publish Post' : 'Save as Draft'}
            </Button>
          </div>
        </div>
      </ModalFooter>
    </Modal>
  )
}
