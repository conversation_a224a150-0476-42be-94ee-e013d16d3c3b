'use client'

import { usePathname } from 'next/navigation'
import { ThemeProvider as NextThemesProvider } from 'next-themes'
import { type ThemeProviderProps } from 'next-themes'
import { useEffect } from 'react'

interface ConditionalThemeProviderProps extends ThemeProviderProps {
  children: React.ReactNode
}

export function ConditionalThemeProvider({ children, ...props }: ConditionalThemeProviderProps) {
  const pathname = usePathname()

  // Completely disable theme provider for admin and auth pages - no theme classes at all
  const isAdminOrAuth = pathname?.startsWith('/admin') || pathname?.startsWith('/auth')

  // Remove any theme classes and cursor elements from HTML when on admin/auth pages
  useEffect(() => {
    if (isAdminOrAuth) {
      // Remove all theme-related classes from html element
      const html = document.documentElement
      html.classList.remove('light', 'dark', 'theme-light', 'theme-dark')

      // Remove theme-related attributes
      html.removeAttribute('data-theme')
      html.removeAttribute('style')

      // Remove any custom cursor elements that might have been created
      const cursorElements = document.querySelectorAll('.cb-cursor, .cb-cursor-text')
      cursorElements.forEach(element => {
        element.remove()
      })

      // Disable any cursor-related event listeners
      if (typeof window !== 'undefined' && window.jQuery) {
        window.jQuery('body').off('mousemove mouseenter mouseleave mousedown mouseup')
      }

      console.log('Admin/Auth page - all theme classes and cursor elements removed')
    }
  }, [isAdminOrAuth, pathname])

  if (isAdminOrAuth) {
    // Return children without any theme provider or theme classes for admin/auth pages
    return <>{children}</>
  }

  // Use theme provider for all other pages
  return <NextThemesProvider {...props}>{children}</NextThemesProvider>
}
