'use client'

import { useState, useEffect, useCallback, memo } from 'react'
import { signIn, getSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import Link from 'next/link'
import {
  EyeIcon,
  EyeSlashIcon,
  EnvelopeIcon,
  LockClosedIcon
} from '@heroicons/react/24/outline'

const signInSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .transform(val => val.toLowerCase().trim()),
  password: z
    .string()
    .min(1, 'Password is required'),
  rememberMe: z.boolean().optional(),
})

type SignInForm = z.infer<typeof signInSchema>



// Memoized error message component
const ErrorMessage = memo<{ error: string | null }>(({ error }) => {
  if (!error) return null

  return (
    <div
      className="rounded-xl bg-red-50 border border-red-200 p-4 animate-in slide-in-from-top-2 duration-300"
      role="alert"
      aria-live="polite"
    >
      <div className="flex">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <p className="text-sm text-red-800">{error}</p>
        </div>
      </div>
    </div>
  )
})
ErrorMessage.displayName = 'ErrorMessage'

// Memoized form field component
const FormField = memo<{
  label: string
  type: 'email' | 'password'
  error?: string
  register: any
  showPassword?: boolean
  onTogglePassword?: () => void
  hasValue: boolean
}>(({ label, type, error, register, showPassword, onTogglePassword, hasValue }) => {
  const Icon = type === 'email' ? EnvelopeIcon : LockClosedIcon
  const inputType = type === 'password' && showPassword ? 'text' : type
  const autoComplete = type === 'email' ? 'email' : 'current-password'

  return (
    <div className="space-y-1">
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Icon className="h-5 w-5 text-slate-400" aria-hidden="true" />
        </div>
        <input
          {...register(type)}
          type={inputType}
          autoComplete={autoComplete}
          aria-invalid={!!error}
          aria-describedby={error ? `${type}-error` : undefined}
          className={`block w-full pl-10 pr-12 py-3 border rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
            error
              ? 'border-red-300 bg-red-50'
              : 'border-slate-300 bg-white'
          } text-slate-900`}
        />
        <label
          className={`absolute left-10 transition-all duration-200 pointer-events-none ${
            hasValue
              ? '-top-2 text-xs bg-white px-2 text-blue-600'
              : 'top-3 text-sm text-slate-400'
          }`}
        >
          {!hasValue && label}
        </label>
        {type === 'password' && (
          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            onClick={onTogglePassword}
            aria-label={showPassword ? 'Hide password' : 'Show password'}
          >
            {showPassword ? (
              <EyeSlashIcon className="h-5 w-5 text-slate-400 hover:text-slate-600 transition-colors" />
            ) : (
              <EyeIcon className="h-5 w-5 text-slate-400 hover:text-slate-600 transition-colors" />
            )}
          </button>
        )}
      </div>
      {error && (
        <p
          id={`${type}-error`}
          className="text-sm text-red-600 animate-in slide-in-from-top-1 duration-200"
          role="alert"
        >
          {error}
        </p>
      )}
    </div>
  )
})
FormField.displayName = 'FormField'

// Memoized footer links component
const FooterLinks = memo(() => (
  <div className="text-center space-y-4">
    <div className="relative">
      <div className="absolute inset-0 flex items-center">
        <div className="w-full border-t border-slate-200" />
      </div>
      <div className="relative flex justify-center text-sm">
        <span className="px-2 bg-white text-slate-500">
          Need help?
        </span>
      </div>
    </div>

    <div className="flex justify-center space-x-4 text-sm">
      <Link
        href="/"
        className="text-slate-600 hover:text-slate-900 transition-colors"
      >
        ← Back to website
      </Link>
      <span className="text-slate-300" aria-hidden="true">|</span>
      <Link
        href="/support"
        className="text-slate-600 hover:text-slate-900 transition-colors"
      >
        Contact support
      </Link>
    </div>
  </div>
))
FooterLinks.displayName = 'FooterLinks'

export function SignInPageClient() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showPassword, setShowPassword] = useState(false)
  const [mounted, setMounted] = useState(false)
  const [emailHasValue, setEmailHasValue] = useState(false)
  const [passwordHasValue, setPasswordHasValue] = useState(false)
  const router = useRouter()

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<SignInForm>({
    resolver: zodResolver(signInSchema),
    defaultValues: {
      rememberMe: false,
    },
  })

  const watchedEmail = watch('email')
  const watchedPassword = watch('password')

  // Optimized input value checking
  const checkInputValue = useCallback((input: HTMLInputElement | null) => {
    if (!input) return false
    return input.value.length > 0 || input.matches(':-webkit-autofill')
  }, [])

  // Single effect for autofill detection with debouncing
  useEffect(() => {
    const checkAutofill = () => {
      const emailInput = document.querySelector('input[type="email"]') as HTMLInputElement
      const passwordInput = document.querySelector('input[type="password"]') as HTMLInputElement
      
      if (emailInput) {
        setEmailHasValue(checkInputValue(emailInput))
      }
      if (passwordInput) {
        setPasswordHasValue(checkInputValue(passwordInput))
      }
    }

    // Check immediately and then with a reasonable interval
    checkAutofill()
    const interval = setInterval(checkAutofill, 500) // Reduced frequency

    return () => clearInterval(interval)
  }, [checkInputValue])

  // Update values when watched values change
  useEffect(() => {
    setEmailHasValue(!!watchedEmail && watchedEmail.length > 0)
  }, [watchedEmail])

  useEffect(() => {
    setPasswordHasValue(!!watchedPassword && watchedPassword.length > 0)
  }, [watchedPassword])

  useEffect(() => {
    setMounted(true)
  }, [])

  const onSubmit = useCallback(async (data: SignInForm) => {
    setIsLoading(true)
    setError(null)

    try {
      // Attempt to sign in
      const result = await signIn('credentials', {
        email: data.email.toLowerCase().trim(),
        password: data.password,
        redirect: false,
      })

      if (result?.error) {
        // Provide specific error messages
        if (result.error === 'CredentialsSignin') {
          setError('Invalid email or password. Please check your credentials and try again.')
        } else {
          setError('Authentication failed. Please try again.')
        }
      } else if (result?.ok) {
        // Get session to check user role
        const session = await getSession()

        if (!session?.user) {
          setError('Authentication failed. Please try again.')
          return
        }

        // Redirect based on user role
        if (session.user.role === 'ADMIN') {
          router.push('/admin')
        } else {
          setError('Access denied. Admin privileges required.')
        }
      } else {
        setError('Authentication failed. Please try again.')
      }
    } catch (error) {
      // Environment-aware error logging
      if (process.env.NODE_ENV === 'development') {
        console.error('Sign-in error:', error)
      }
      setError('An unexpected error occurred. Please try again later.')
    } finally {
      setIsLoading(false)
    }
  }, [router])

  const togglePassword = useCallback(() => {
    setShowPassword(prev => !prev)
  }, [])

  if (!mounted) {
    return null
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 py-12 px-4 sm:px-6 lg:px-8">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-slate-100 bg-[size:20px_20px] opacity-60" />
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/50 to-transparent" />

      <div className="relative w-full max-w-md space-y-8">
        {/* Login Card */}
        <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-8 space-y-8">
          {/* Header */}
          <div className="text-center">
            <div className="mx-auto h-16 w-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg">
              <span className="text-2xl font-bold text-white">T</span>
            </div>
            <h1 className="text-3xl font-bold text-slate-900">
              Welcome Back
            </h1>
            <p className="mt-2 text-sm text-slate-600">
              Sign in to your admin dashboard
            </p>
          </div>

          <ErrorMessage error={error} />

          {/* Form */}
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)} noValidate>
            <FormField
              label="Email address"
              type="email"
              error={errors.email?.message}
              register={register}
              hasValue={emailHasValue}
            />

            <FormField
              label="Password"
              type="password"
              error={errors.password?.message}
              register={register}
              showPassword={showPassword}
              onTogglePassword={togglePassword}
              hasValue={passwordHasValue}
            />

            {/* Remember Me & Forgot Password */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  {...register('rememberMe')}
                  id="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded bg-white"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-slate-700">
                  Remember me
                </label>
              </div>

              <div className="text-sm">
                <Link
                  href="/auth/forgot-password"
                  className="font-medium text-blue-600 hover:text-blue-500 transition-colors"
                >
                  Forgot password?
                </Link>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transform transition-all duration-200 hover:scale-[1.02] active:scale-[0.98] shadow-lg hover:shadow-xl"
              aria-describedby={isLoading ? 'loading-description' : undefined}
            >
              <span className="absolute left-0 inset-y-0 flex items-center pl-3">
                {isLoading ? (
                  <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" aria-hidden="true">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : (
                  <LockClosedIcon className="h-5 w-5 text-white/70 group-hover:text-white transition-colors" aria-hidden="true" />
                )}
              </span>
              {isLoading ? 'Signing in...' : 'Sign in to Dashboard'}
              {isLoading && (
                <span id="loading-description" className="sr-only">
                  Please wait while we sign you in
                </span>
              )}
            </button>
          </form>

          <FooterLinks />
        </div>
      </div>
    </div>
  )
}
